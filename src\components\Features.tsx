'use client';

import React, { useState } from 'react';

const Features = () => {
  const [activeView, setActiveView] = useState<'cliente' | 'estabelecimento'>('cliente');

  const handleFeatureClick = (featureName: string, userType: string) => {
    console.log(`Feature clicada: ${featureName} - ${userType}`);
    alert(`Saiba mais sobre: ${featureName} para ${userType}`);
  };

  const handleViewChange = (view: 'cliente' | 'estabelecimento') => {
    setActiveView(view);
    console.log(`Visualização alterada para: ${view}`);
  };

  // Features para clientes
  const clientFeatures = [
    {
      id: 'busca-facil',
      title: 'Busca Inteligente',
      description: 'Encontre estabelecimentos por localização, tipo de serviço, avaliações e disponibilidade em tempo real.',
      icon: '🔍',
      color: 'blue'
    },
    {
      id: 'agendamento-rapido',
      title: 'Agendamento Rápido',
      description: 'Agende seus serviços em poucos cliques, escolha profissionais e veja horários disponíveis instantaneamente.',
      icon: '⚡',
      color: 'green'
    },
    {
      id: 'pagamento-seguro',
      title: 'Pagamento Seguro',
      description: 'Pague online com Pix, cartão ou escolha pagar no local. Transações 100% seguras e protegidas.',
      icon: '🔒',
      color: 'purple'
    },
    {
      id: 'avaliacoes',
      title: 'Avaliações Reais',
      description: 'Veja avaliações e fotos de outros clientes para escolher o melhor estabelecimento para você.',
      icon: '⭐',
      color: 'indigo'
    },
    {
      id: 'lembretes',
      title: 'Lembretes Automáticos',
      description: 'Receba notificações por email e SMS para não perder seus agendamentos importantes.',
      icon: '🔔',
      color: 'pink'
    },
    {
      id: 'historico',
      title: 'Histórico Completo',
      description: 'Acesse todo seu histórico de agendamentos, favoritos e avaliações em um só lugar.',
      icon: '📋',
      color: 'yellow'
    }
  ];

  // Features para estabelecimentos
  const businessFeatures = [
    {
      id: 'agendamento-online',
      title: 'Agendamento 24/7',
      description: 'Receba agendamentos online a qualquer hora. Sistema inteligente de confirmação e gestão de horários.',
      icon: '📅',
      color: 'blue'
    },
    {
      id: 'gestao-equipe',
      title: 'Gestão de Equipe',
      description: 'Gerencie colaboradores, defina permissões, controle comissões e acompanhe performance individual.',
      icon: '👥',
      color: 'green'
    },
    {
      id: 'pagamentos-integrados',
      title: 'Pagamentos Integrados',
      description: 'Aceite Pix, cartão de débito e crédito. Reembolsos automáticos e controle financeiro completo.',
      icon: '💳',
      color: 'purple'
    },
    {
      id: 'relatorios-detalhados',
      title: 'Relatórios Detalhados',
      description: 'Acompanhe faturamento, agendamentos e performance com relatórios inteligentes e insights valiosos.',
      icon: '📊',
      color: 'indigo'
    },
    {
      id: 'marketing-automatico',
      title: 'Marketing Automático',
      description: 'Envie campanhas por email/SMS, crie cupons de desconto e fidelize clientes automaticamente.',
      icon: '📢',
      color: 'pink'
    },
    {
      id: 'suporte-especializado',
      title: 'Suporte Especializado',
      description: 'Equipe dedicada para ajudar seu negócio crescer. Suporte prioritário no plano Premium.',
      icon: '🎧',
      color: 'yellow'
    }
  ];

  const currentFeatures = activeView === 'cliente' ? clientFeatures : businessFeatures;

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-100 text-blue-600 group-hover:bg-blue-600 group-hover:text-white',
      green: 'bg-green-100 text-green-600 group-hover:bg-green-600 group-hover:text-white',
      purple: 'bg-purple-100 text-purple-600 group-hover:bg-purple-600 group-hover:text-white',
      indigo: 'bg-indigo-100 text-indigo-600 group-hover:bg-indigo-600 group-hover:text-white',
      pink: 'bg-pink-100 text-pink-600 group-hover:bg-pink-600 group-hover:text-white',
      yellow: 'bg-yellow-100 text-yellow-600 group-hover:bg-yellow-600 group-hover:text-white'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <section id="funcionalidades" className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header da Seção */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {activeView === 'cliente' ? (
              <>
                Por que escolher o
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                  {' '}AgendaPro?
                </span>
              </>
            ) : (
              <>
                Tudo que você precisa para
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                  {' '}crescer seu negócio
                </span>
              </>
            )}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            {activeView === 'cliente'
              ? 'Descubra todas as vantagens de usar nossa plataforma para agendar seus serviços favoritos.'
              : 'Uma plataforma completa com todas as ferramentas essenciais para modernizar e otimizar a gestão do seu estabelecimento.'
            }
          </p>

          {/* Seletor de Visualização */}
          <div className="inline-flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => handleViewChange('cliente')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                activeView === 'cliente'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              👤 Para Clientes
            </button>
            <button
              onClick={() => handleViewChange('estabelecimento')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                activeView === 'estabelecimento'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              🏪 Para Estabelecimentos
            </button>
          </div>
        </div>

        {/* Grid de Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {currentFeatures.map((feature) => (
            <div
              key={feature.id}
              onClick={() => handleFeatureClick(feature.title, activeView)}
              className="group bg-white rounded-xl p-6 border border-gray-200 hover:border-transparent hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1"
            >
              {/* Ícone Emoji */}
              <div className="text-4xl mb-4">
                {feature.icon}
              </div>

              {/* Título */}
              <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-gray-800">
                {feature.title}
              </h3>

              {/* Descrição */}
              <p className="text-gray-600 leading-relaxed group-hover:text-gray-700">
                {feature.description}
              </p>

              {/* Seta indicativa */}
              <div className="mt-4 flex items-center text-sm font-medium text-gray-400 group-hover:text-blue-600 transition-colors">
                Saiba mais
                <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Pronto para revolucionar seu negócio?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Junte-se a centenas de estabelecimentos que já transformaram sua gestão com o AgendaPro.
            </p>
            <button
              onClick={() => handleFeatureClick('Começar Agora')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Começar Agora - É Grátis
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
