'use client';

import React, { useState } from 'react';

const MarketplaceShowcase = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('todos');
  const [selectedCity, setSelectedCity] = useState<string>('todas');

  // Dados mockados de estabelecimentos em destaque
  const establishments = [
    {
      id: 1,
      name: 'Barbearia Moderna',
      category: 'barbearia',
      city: 'São Paulo',
      rating: 4.9,
      reviewCount: 127,
      image: '🪒',
      services: ['Corte Masculino', 'Barba', 'Bigode'],
      priceRange: 'R$ 25 - R$ 45',
      distance: '1.2 km',
      nextAvailable: 'Hoje às 14:30'
    },
    {
      id: 2,
      name: 'Salão Elegance',
      category: 'salao',
      city: 'Rio de Janeiro',
      rating: 4.8,
      reviewCount: 89,
      image: '💇‍♀️',
      services: ['Corte Feminino', 'Escova', 'Coloração'],
      priceRange: 'R$ 40 - R$ 120',
      distance: '2.1 km',
      nextAvailable: 'Amanhã às 09:00'
    },
    {
      id: 3,
      name: 'Clínica Estética Bella',
      category: 'clinica',
      city: 'Belo Horizonte',
      rating: 4.7,
      reviewCount: 156,
      image: '✨',
      services: ['Limpeza de Pele', 'Massagem', 'Depilação'],
      priceRange: 'R$ 60 - R$ 200',
      distance: '3.5 km',
      nextAvailable: 'Hoje às 16:00'
    },
    {
      id: 4,
      name: 'Studio Hair & Beauty',
      category: 'salao',
      city: 'São Paulo',
      rating: 4.9,
      reviewCount: 203,
      image: '💄',
      services: ['Corte', 'Manicure', 'Maquiagem'],
      priceRange: 'R$ 35 - R$ 80',
      distance: '0.8 km',
      nextAvailable: 'Hoje às 15:45'
    },
    {
      id: 5,
      name: 'Barbearia Clássica',
      category: 'barbearia',
      city: 'Porto Alegre',
      rating: 4.6,
      reviewCount: 94,
      image: '✂️',
      services: ['Corte Tradicional', 'Barba', 'Relaxamento'],
      priceRange: 'R$ 20 - R$ 40',
      distance: '1.7 km',
      nextAvailable: 'Amanhã às 10:30'
    },
    {
      id: 6,
      name: 'Spa & Wellness Center',
      category: 'clinica',
      city: 'Brasília',
      rating: 4.8,
      reviewCount: 78,
      image: '🧘‍♀️',
      services: ['Massagem Relaxante', 'Tratamento Facial', 'Aromaterapia'],
      priceRange: 'R$ 80 - R$ 250',
      distance: '2.3 km',
      nextAvailable: 'Hoje às 17:30'
    }
  ];

  const categories = [
    { id: 'todos', name: 'Todos', icon: '🏪' },
    { id: 'barbearia', name: 'Barbearias', icon: '🪒' },
    { id: 'salao', name: 'Salões', icon: '💇‍♀️' },
    { id: 'clinica', name: 'Clínicas', icon: '✨' }
  ];

  const cities = [
    'todas', 'São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Porto Alegre', 'Brasília'
  ];

  // Filtrar estabelecimentos
  const filteredEstablishments = establishments.filter(establishment => {
    const categoryMatch = selectedCategory === 'todos' || establishment.category === selectedCategory;
    const cityMatch = selectedCity === 'todas' || establishment.city === selectedCity;
    return categoryMatch && cityMatch;
  });

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    console.log(`Categoria selecionada: ${category}`);
  };

  const handleCityChange = (city: string) => {
    setSelectedCity(city);
    console.log(`Cidade selecionada: ${city}`);
  };

  const handleEstablishmentClick = (establishment: any) => {
    console.log(`Estabelecimento clicado: ${establishment.name}`);
    alert(`Redirecionando para a página de ${establishment.name}. Aqui você poderá ver todos os serviços, horários disponíveis e fazer seu agendamento!`);
  };

  const handleBookNowClick = (establishment: any, event: React.MouseEvent) => {
    event.stopPropagation(); // Evita trigger do card
    console.log(`Agendamento rápido para: ${establishment.name}`);
    alert(`Iniciando agendamento rápido em ${establishment.name}. Próximo horário disponível: ${establishment.nextAvailable}`);
  };

  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const stars = [];

    for (let i = 0; i < fullStars; i++) {
      stars.push(<span key={i} className="text-yellow-400">★</span>);
    }
    
    if (hasHalfStar) {
      stars.push(<span key="half" className="text-yellow-400">☆</span>);
    }

    return stars;
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header da Seção */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Descubra os melhores
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              {' '}estabelecimentos{' '}
            </span>
            perto de você
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Mais de 500 estabelecimentos parceiros em todo o Brasil. 
            Encontre, compare e agende seus serviços favoritos em poucos cliques.
          </p>
        </div>

        {/* Filtros */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-center">
            {/* Filtro de Categoria */}
            <div className="flex flex-wrap gap-2 justify-center">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategoryChange(category.id)}
                  className={`flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    selectedCategory === category.id
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                  }`}
                >
                  <span className="mr-2">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </div>

            {/* Filtro de Cidade */}
            <div className="flex items-center">
              <span className="text-gray-700 mr-3 font-medium">📍 Cidade:</span>
              <select
                value={selectedCity}
                onChange={(e) => handleCityChange(e.target.value)}
                className="bg-white border border-gray-200 rounded-lg px-4 py-2 text-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {cities.map((city) => (
                  <option key={city} value={city}>
                    {city === 'todas' ? 'Todas as cidades' : city}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Grid de Estabelecimentos */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredEstablishments.map((establishment) => (
            <div
              key={establishment.id}
              onClick={() => handleEstablishmentClick(establishment)}
              className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105 overflow-hidden"
            >
              {/* Header do Card */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className="text-3xl mr-3">{establishment.image}</div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900">{establishment.name}</h3>
                      <p className="text-sm text-gray-600">{establishment.city}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center">
                      {renderStars(establishment.rating)}
                      <span className="ml-1 text-sm font-medium text-gray-900">{establishment.rating}</span>
                    </div>
                    <p className="text-xs text-gray-500">({establishment.reviewCount} avaliações)</p>
                  </div>
                </div>

                {/* Serviços */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {establishment.services.slice(0, 3).map((service, index) => (
                      <span
                        key={index}
                        className="bg-blue-50 text-blue-700 px-2 py-1 rounded-full text-xs font-medium"
                      >
                        {service}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Informações */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">💰 Preços:</span>
                    <span className="font-medium text-gray-900">{establishment.priceRange}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">📍 Distância:</span>
                    <span className="font-medium text-gray-900">{establishment.distance}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">⏰ Próximo horário:</span>
                    <span className="font-medium text-green-600">{establishment.nextAvailable}</span>
                  </div>
                </div>

                {/* Botão de Ação */}
                <button
                  onClick={(e) => handleBookNowClick(establishment, e)}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105"
                >
                  Agendar Agora
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Estatísticas */}
        <div className="mt-16 bg-white rounded-2xl p-8 shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">500+</div>
              <div className="text-gray-600">Estabelecimentos Parceiros</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600 mb-2">10k+</div>
              <div className="text-gray-600">Agendamentos por Mês</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600 mb-2">50+</div>
              <div className="text-gray-600">Cidades Atendidas</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-600 mb-2">4.8★</div>
              <div className="text-gray-600">Avaliação Média</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MarketplaceShowcase;
