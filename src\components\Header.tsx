'use client';

import React, { useState } from 'react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleMenuToggle = () => {
    setIsMenuOpen(!isMenuOpen);
    console.log('Menu toggle:', !isMenuOpen);
  };

  const handleNavClick = (section: string) => {
    console.log(`Navegando para seção: ${section}`);
    setIsMenuOpen(false);
  };

  const handleLoginClick = (userType?: string) => {
    console.log(`Botão Login clicado - ${userType || 'geral'}`);
    alert(`Login ${userType ? `para ${userType}` : ''} será implementado em breve!`);
  };

  const handleSignupClick = (userType?: string) => {
    console.log(`Botão Cadastro clicado - ${userType || 'geral'}`);
    alert(`Cadastro ${userType ? `para ${userType}` : ''} será implementado em breve!`);
  };

  const handleMarketplaceClick = () => {
    console.log('Navegando para marketplace');
    alert('Redirecionando para o marketplace de estabelecimentos!');
  };

  const handleBusinessClick = () => {
    console.log('Navegando para área de estabelecimentos');
    alert('Redirecionando para a área de estabelecimentos!');
  };

  return (
    <header className="bg-white shadow-sm fixed w-full top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AP</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">AgendaPro</span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-6">
            <button
              onClick={handleMarketplaceClick}
              className="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
            >
              🔍 Encontrar Serviços
            </button>
            <button
              onClick={handleBusinessClick}
              className="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
            >
              🏪 Para Estabelecimentos
            </button>
            <button
              onClick={() => handleNavClick('como-funciona')}
              className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
            >
              Como Funciona
            </button>
            <button
              onClick={() => handleNavClick('planos')}
              className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
            >
              Planos
            </button>
          </nav>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-3">
            {/* Dropdown de Login */}
            <div className="relative group">
              <button className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors flex items-center">
                Entrar
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                <div className="py-1">
                  <button
                    onClick={() => handleLoginClick('cliente')}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    👤 Login Cliente
                  </button>
                  <button
                    onClick={() => handleLoginClick('estabelecimento')}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    🏪 Login Estabelecimento
                  </button>
                </div>
              </div>
            </div>

            {/* Dropdown de Cadastro */}
            <div className="relative group">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center">
                Cadastrar
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                <div className="py-1">
                  <button
                    onClick={() => handleSignupClick('cliente')}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    👤 Cadastro Cliente
                  </button>
                  <button
                    onClick={() => handleSignupClick('estabelecimento')}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    🏪 Cadastro Estabelecimento
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={handleMenuToggle}
              className="text-gray-700 hover:text-blue-600 p-2"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t">
              <button
                onClick={handleMarketplaceClick}
                className="flex items-center w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors"
              >
                🔍 Encontrar Serviços
              </button>
              <button
                onClick={handleBusinessClick}
                className="flex items-center w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors"
              >
                🏪 Para Estabelecimentos
              </button>
              <button
                onClick={() => handleNavClick('como-funciona')}
                className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors"
              >
                Como Funciona
              </button>
              <button
                onClick={() => handleNavClick('planos')}
                className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors"
              >
                Planos
              </button>

              {/* Seção de Login Mobile */}
              <div className="pt-4 pb-2 border-t border-gray-200">
                <div className="mb-3">
                  <p className="px-3 text-sm font-medium text-gray-500 mb-2">Login</p>
                  <button
                    onClick={() => handleLoginClick('cliente')}
                    className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    👤 Cliente
                  </button>
                  <button
                    onClick={() => handleLoginClick('estabelecimento')}
                    className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    🏪 Estabelecimento
                  </button>
                </div>

                <div>
                  <p className="px-3 text-sm font-medium text-gray-500 mb-2">Cadastrar</p>
                  <button
                    onClick={() => handleSignupClick('cliente')}
                    className="block w-full text-left px-3 py-2 mb-2 bg-gray-100 hover:bg-gray-200 text-gray-900 rounded-md text-base font-medium transition-colors"
                  >
                    👤 Cadastro Cliente
                  </button>
                  <button
                    onClick={() => handleSignupClick('estabelecimento')}
                    className="block w-full text-left px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-base font-medium transition-colors"
                  >
                    🏪 Cadastro Estabelecimento
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
