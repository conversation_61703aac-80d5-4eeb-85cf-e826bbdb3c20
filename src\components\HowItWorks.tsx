'use client';

import React, { useState } from 'react';

const HowItWorks = () => {
  const [activeFlow, setActiveFlow] = useState<'cliente' | 'estabelecimento'>('cliente');

  const handleStepClick = (stepNumber: number, stepTitle: string, userType: string) => {
    console.log(`Passo ${stepNumber} clicado: ${stepTitle} - ${userType}`);
    alert(`Saiba mais sobre o Passo ${stepNumber} para ${userType}: ${stepTitle}`);
  };

  const handleFlowChange = (flow: 'cliente' | 'estabelecimento') => {
    setActiveFlow(flow);
    console.log(`Fluxo alterado para: ${flow}`);
  };

  // Fluxo para clientes
  const clientSteps = [
    {
      number: 1,
      title: 'Encontre Estabelecimentos',
      description: 'Busque por salões, barbearias e clínicas perto de você. Veja fotos, avaliações e serviços disponíveis.',
      icon: '🔍',
      color: 'blue'
    },
    {
      number: 2,
      title: 'Escolha Serviço e Horário',
      description: 'Selecione o serviço desejado, escolha o profissional (opcional) e veja os horários disponíveis em tempo real.',
      icon: '📅',
      color: 'purple'
    },
    {
      number: 3,
      title: 'Faça o Pagamento',
      description: 'Pague online com segurança via Pix, cartão ou escolha pagar no local. Receba confirmação instantânea.',
      icon: '💳',
      color: 'green'
    },
    {
      number: 4,
      title: 'Compareça e Avalie',
      description: 'Receba lembretes do agendamento, compareça no horário marcado e avalie sua experiência.',
      icon: '⭐',
      color: 'indigo'
    }
  ];

  // Fluxo para estabelecimentos
  const businessSteps = [
    {
      number: 1,
      title: 'Escolha seu Plano',
      description: 'Selecione o plano ideal para seu estabelecimento. Essencial para começar ou Premium para recursos avançados.',
      icon: '💰',
      color: 'blue'
    },
    {
      number: 2,
      title: 'Configure sua Empresa',
      description: 'Preencha os dados do seu estabelecimento, cadastre serviços, defina horários e configure sua equipe.',
      icon: '⚙️',
      color: 'purple'
    },
    {
      number: 3,
      title: 'Receba Agendamentos',
      description: 'Seus clientes podem agendar online 24/7. Você recebe notificações e pode confirmar ou recusar agendamentos.',
      icon: '📋',
      color: 'green'
    },
    {
      number: 4,
      title: 'Gerencie e Cresça',
      description: 'Acompanhe relatórios, gerencie pagamentos, use ferramentas de marketing e faça seu negócio crescer.',
      icon: '📈',
      color: 'indigo'
    }
  ];

  const currentSteps = activeFlow === 'cliente' ? clientSteps : businessSteps;

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: 'bg-blue-100',
        text: 'text-blue-600',
        border: 'border-blue-200',
        gradient: 'from-blue-500 to-blue-600'
      },
      purple: {
        bg: 'bg-purple-100',
        text: 'text-purple-600',
        border: 'border-purple-200',
        gradient: 'from-purple-500 to-purple-600'
      },
      green: {
        bg: 'bg-green-100',
        text: 'text-green-600',
        border: 'border-green-200',
        gradient: 'from-green-500 to-green-600'
      },
      indigo: {
        bg: 'bg-indigo-100',
        text: 'text-indigo-600',
        border: 'border-indigo-200',
        gradient: 'from-indigo-500 to-indigo-600'
      }
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <section id="como-funciona" className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header da Seção */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Como funciona o
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              {' '}AgendaPro
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            {activeFlow === 'cliente'
              ? 'Agende seus serviços favoritos em 4 passos simples e rápidos.'
              : 'Em apenas 4 passos simples, você terá sua plataforma de agendamento funcionando e recebendo clientes.'
            }
          </p>

          {/* Seletor de Fluxo */}
          <div className="inline-flex bg-white rounded-lg p-1 shadow-lg">
            <button
              onClick={() => handleFlowChange('cliente')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                activeFlow === 'cliente'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              👤 Para Clientes
            </button>
            <button
              onClick={() => handleFlowChange('estabelecimento')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                activeFlow === 'estabelecimento'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              🏪 Para Estabelecimentos
            </button>
          </div>
        </div>

        {/* Steps */}
        <div className="relative">
          {/* Linha conectora - Desktop */}
          <div className="hidden lg:block absolute top-24 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-200 via-purple-200 via-green-200 to-indigo-200"></div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-4">
            {currentSteps.map((step, index) => {
              const colors = getColorClasses(step.color);

              return (
                <div
                  key={step.number}
                  onClick={() => handleStepClick(step.number, step.title, activeFlow)}
                  className="relative group cursor-pointer"
                >
                  {/* Linha conectora - Mobile */}
                  {index < currentSteps.length - 1 && (
                    <div className="lg:hidden absolute left-6 top-16 w-0.5 h-16 bg-gray-200"></div>
                  )}

                  <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 relative z-10">
                    {/* Número do Passo */}
                    <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r ${colors.gradient} text-white font-bold text-lg mb-4 shadow-lg`}>
                      {step.number}
                    </div>

                    {/* Ícone Emoji */}
                    <div className="text-4xl mb-4 text-center">
                      {step.icon}
                    </div>

                    {/* Título */}
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      {step.title}
                    </h3>

                    {/* Descrição */}
                    <p className="text-gray-600 leading-relaxed">
                      {step.description}
                    </p>

                    {/* Indicador de interação */}
                    <div className="mt-4 flex items-center text-sm font-medium text-gray-400 group-hover:text-blue-600 transition-colors">
                      Clique para saber mais
                      <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Seção de Tempo/Benefícios */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <div className="flex items-center justify-center mb-6">
              <div className="bg-green-100 rounded-full p-3">
                {activeFlow === 'cliente' ? (
                  <span className="text-2xl">⚡</span>
                ) : (
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {activeFlow === 'cliente'
                ? 'Agendamento em menos de 2 minutos'
                : 'Configuração em menos de 30 minutos'
              }
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              {activeFlow === 'cliente'
                ? 'Nossa plataforma é super rápida e intuitiva. Encontre, agende e pague seus serviços favoritos em poucos cliques, a qualquer hora do dia.'
                : 'Nossa interface intuitiva e o wizard de configuração guiado tornam o processo rápido e simples, mesmo para quem não tem experiência técnica.'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {activeFlow === 'cliente' ? (
                <>
                  <button
                    onClick={() => handleStepClick(0, 'Encontrar Serviços', 'cliente')}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    🔍 Encontrar Serviços
                  </button>
                  <button
                    onClick={() => handleStepClick(0, 'Ver Estabelecimentos', 'cliente')}
                    className="border-2 border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-8 py-3 rounded-lg font-semibold transition-all duration-200"
                  >
                    📍 Ver Estabelecimentos
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => handleStepClick(0, 'Começar Configuração', 'estabelecimento')}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    🚀 Começar Configuração
                  </button>
                  <button
                    onClick={() => handleStepClick(0, 'Agendar Demonstração', 'estabelecimento')}
                    className="border-2 border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-8 py-3 rounded-lg font-semibold transition-all duration-200"
                  >
                    📞 Agendar Demonstração
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
