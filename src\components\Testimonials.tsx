'use client';

import React, { useState } from 'react';

const Testimonials = () => {
  const [activeTab, setActiveTab] = useState<'clientes' | 'estabelecimentos'>('clientes');

  // Depoimentos de clientes
  const clientTestimonials = [
    {
      id: 1,
      name: '<PERSON>',
      location: 'São Paulo, SP',
      avatar: '👩‍💼',
      rating: 5,
      text: 'Incrível como é fácil encontrar e agendar serviços! Já usei para corte de cabelo e manicure. A plataforma é super intuitiva e os estabelecimentos são de qualidade.',
      service: 'Corte + Escova',
      establishment: 'Salão Elegance'
    },
    {
      id: 2,
      name: '<PERSON>',
      location: 'Rio de Janeiro, RJ',
      avatar: '👨‍💻',
      rating: 5,
      text: 'Melhor decisão foi descobrir esta plataforma. Consigo agendar meu corte de cabelo e barba sem precisar ligar ou sair de casa. Pagamento online é muito prático!',
      service: 'Corte + Bar<PERSON>',
      establishment: 'Barbearia Moderna'
    },
    {
      id: 3,
      name: '<PERSON>',
      location: 'Belo Horizonte, MG',
      avatar: '👩‍🎨',
      rating: 5,
      text: '<PERSON><PERSON> poder ver as avaliações e fotos antes de escolher. Já experimentei 3 salões diferentes através da plataforma e todos foram excelentes!',
      service: 'Coloração + Corte',
      establishment: 'Studio Hair & Beauty'
    },
    {
      id: 4,
      name: 'Carlos <PERSON>',
      location: 'Porto Alegre, RS',
      avatar: '👨‍🏫',
      rating: 4,
      text: 'Praticidade total! Posso agendar durante o trabalho e ainda recebo lembretes. Os preços são transparentes e não tem surpresas.',
      service: 'Massagem Relaxante',
      establishment: 'Spa & Wellness'
    }
  ];

  // Depoimentos de estabelecimentos
  const businessTestimonials = [
    {
      id: 1,
      name: 'Roberto Ferreira',
      role: 'Proprietário',
      business: 'Barbearia Moderna',
      location: 'São Paulo, SP',
      avatar: '👨‍💼',
      rating: 5,
      text: 'Desde que aderi à plataforma, meus agendamentos aumentaram 40%. O sistema é muito fácil de usar e meus clientes adoram a praticidade do agendamento online.',
      metrics: '+40% agendamentos',
      plan: 'Premium'
    },
    {
      id: 2,
      name: 'Fernanda Lima',
      role: 'Proprietária',
      business: 'Salão Elegance',
      location: 'Rio de Janeiro, RJ',
      avatar: '👩‍💼',
      rating: 5,
      text: 'A plataforma revolucionou meu negócio! Consigo gerenciar toda minha equipe, horários e pagamentos em um só lugar. Os relatórios me ajudam muito na gestão.',
      metrics: 'R$ 15k+ faturamento/mês',
      plan: 'Premium'
    },
    {
      id: 3,
      name: 'Dr. Paulo Mendes',
      role: 'Proprietário',
      business: 'Clínica Estética Bella',
      location: 'Belo Horizonte, MG',
      avatar: '👨‍⚕️',
      rating: 5,
      text: 'Excelente ferramenta para clínicas. O controle de agendamentos é perfeito e a integração com pagamentos online facilitou muito nossa operação.',
      metrics: '95% ocupação',
      plan: 'Premium'
    },
    {
      id: 4,
      name: 'Juliana Rocha',
      role: 'Proprietária',
      business: 'Studio Hair & Beauty',
      location: 'São Paulo, SP',
      avatar: '👩‍🎨',
      rating: 4,
      text: 'Comecei com o plano Essencial e já estou vendo resultados. A facilidade para meus clientes agendarem online fez toda a diferença no meu faturamento.',
      metrics: '+25% novos clientes',
      plan: 'Essencial'
    }
  ];

  const handleTabChange = (tab: 'clientes' | 'estabelecimentos') => {
    setActiveTab(tab);
    console.log(`Aba de depoimentos alterada para: ${tab}`);
  };

  const handleTestimonialClick = (testimonial: any) => {
    console.log(`Depoimento clicado: ${testimonial.name}`);
    if (activeTab === 'clientes') {
      alert(`Depoimento de ${testimonial.name} sobre ${testimonial.establishment}. Serviço: ${testimonial.service}`);
    } else {
      alert(`Depoimento de ${testimonial.name} - ${testimonial.business} (Plano ${testimonial.plan}). Resultado: ${testimonial.metrics}`);
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 0; i < 5; i++) {
      stars.push(
        <span key={i} className={i < rating ? 'text-yellow-400' : 'text-gray-300'}>
          ★
        </span>
      );
    }
    return stars;
  };

  const currentTestimonials = activeTab === 'clientes' ? clientTestimonials : businessTestimonials;

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header da Seção */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            O que nossos
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              {' '}usuários dizem
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Milhares de clientes satisfeitos e centenas de estabelecimentos que transformaram 
            seus negócios com nossa plataforma.
          </p>

          {/* Seletor de Abas */}
          <div className="inline-flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => handleTabChange('clientes')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                activeTab === 'clientes'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              👥 Clientes
            </button>
            <button
              onClick={() => handleTabChange('estabelecimentos')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                activeTab === 'estabelecimentos'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              🏪 Estabelecimentos
            </button>
          </div>
        </div>

        {/* Grid de Depoimentos */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {currentTestimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              onClick={() => handleTestimonialClick(testimonial)}
              className="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-all duration-300 cursor-pointer transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              {/* Header do Depoimento */}
              <div className="flex items-start mb-4">
                <div className="text-4xl mr-4">{testimonial.avatar}</div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-bold text-gray-900">{testimonial.name}</h4>
                      <p className="text-sm text-gray-600">
                        {activeTab === 'clientes' 
                          ? testimonial.location 
                          : `${(testimonial as any).role} - ${(testimonial as any).business}`
                        }
                      </p>
                    </div>
                    <div className="flex">
                      {renderStars(testimonial.rating)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Texto do Depoimento */}
              <blockquote className="text-gray-700 mb-4 italic">
                "{testimonial.text}"
              </blockquote>

              {/* Informações Adicionais */}
              <div className="border-t border-gray-200 pt-4">
                {activeTab === 'clientes' ? (
                  <div className="flex items-center justify-between text-sm">
                    <div>
                      <span className="text-gray-600">Serviço: </span>
                      <span className="font-medium text-gray-900">{(testimonial as any).service}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Local: </span>
                      <span className="font-medium text-blue-600">{(testimonial as any).establishment}</span>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                        Plano {(testimonial as any).plan}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-green-600">
                        {(testimonial as any).metrics}
                      </div>
                      <div className="text-xs text-gray-500">{(testimonial as any).location}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Estatísticas de Satisfação */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              Números que comprovam nossa qualidade
            </h3>
            <p className="text-gray-600">
              Resultados reais de quem confia na nossa plataforma
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">98%</div>
              <div className="text-gray-600">Taxa de Satisfação</div>
              <div className="text-sm text-gray-500 mt-1">Clientes satisfeitos</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600 mb-2">4.8★</div>
              <div className="text-gray-600">Avaliação Média</div>
              <div className="text-sm text-gray-500 mt-1">Estabelecimentos</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600 mb-2">95%</div>
              <div className="text-gray-600">Taxa de Retenção</div>
              <div className="text-sm text-gray-500 mt-1">Estabelecimentos ativos</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-600 mb-2">24h</div>
              <div className="text-gray-600">Suporte Médio</div>
              <div className="text-sm text-gray-500 mt-1">Tempo de resposta</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
